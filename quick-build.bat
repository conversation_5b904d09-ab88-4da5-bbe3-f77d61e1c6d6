@echo off
REM ================================================================================
REM Quick Build Script for File Transfer SDK (Windows Version)
REM ================================================================================

echo ========================================================
echo     File Transfer SDK Quick Build Script
echo     Version: 1.0.0
echo     Time: %date% %time%
echo ========================================================
echo.

REM Check if this is build or build-test mode
set "MODE=build"
if "%1"=="build-test" set "MODE=build-test"

echo [INFO] Execution mode: %MODE%
echo.

REM Step 1: Verify Java
echo [STEP 1] Checking Java environment...
where java >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java not found in PATH
    echo Please install Java 8 or higher and add it to PATH
    exit /b 1
)

java -version 2>&1 | findstr "version"
echo [SUCCESS] Java environment ready

REM Step 2: Check Maven
echo.
echo [STEP 2] Checking Maven environment...
where mvn >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Maven not found in PATH
    echo Please install Apache Maven and add it to PATH
    exit /b 1
)

mvn -version 2>&1 | findstr "Apache Maven"
echo [SUCCESS] Maven environment ready

REM Step 3: Validate project structure
echo.
echo [STEP 3] Validating project structure...
if not exist "pom.xml" (
    echo [ERROR] Root pom.xml not found
    exit /b 1
)
echo [SUCCESS] Project structure validated

REM Step 4: Clean environment
echo.
echo [STEP 4] Cleaning build environment...
if exist "target" rmdir /s /q "target" 2>nul
for %%m in (file-transfer-server-sdk file-transfer-client-sdk file-transfer-client-demo file-transfer-server-standalone) do (
    if exist "%%m\target" (
        rmdir /s /q "%%m\target" 2>nul
        echo [INFO] Cleaned: %%m\target
    )
)
echo [SUCCESS] Environment cleaned

REM Step 5: Compile project
echo.
echo [STEP 5] Compiling project...
echo [INFO] Running: mvn clean compile -T 1C
mvn clean compile -T 1C
if errorlevel 1 (
    echo [ERROR] Project compilation failed
    echo Please check the error messages above
    exit /b 1
)
echo [SUCCESS] Project compilation completed

REM Step 6: Install project
echo.
echo [STEP 6] Installing project to local Maven repository...
echo [INFO] Running: mvn install -DskipTests -T 1C
mvn install -DskipTests -T 1C
if errorlevel 1 (
    echo [ERROR] Project installation failed
    echo Please check the error messages above
    exit /b 1
)
echo [SUCCESS] Project installation completed

REM Step 7: Run tests (if in build-test mode)
if "%MODE%"=="build-test" (
    echo.
    echo [STEP 7] Running unit tests...
    echo [INFO] Running: mvn test
    mvn test
    if errorlevel 1 (
        echo [ERROR] Unit tests failed
        echo Please check the error messages above
        exit /b 1
    )
    echo [SUCCESS] Unit tests completed
)

echo.
echo ========================================================
if "%MODE%"=="build" (
    echo [SUCCESS] Build completed successfully!
) else (
    echo [SUCCESS] Build and test completed successfully!
)
echo ========================================================
echo.

echo [INFO] Next steps:
echo   1. To start the server: cd file-transfer-server-standalone
echo   2. Then run: start-server.bat start --background
echo   3. To run client demo: cd file-transfer-client-demo
echo   4. Then run: mvn exec:java
echo.

exit /b 0
