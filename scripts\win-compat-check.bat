@echo off
REM ================================================================================
REM Windows Compatibility Check Script
REM Purpose: Verify basic functionality in Windows environment
REM ================================================================================

setlocal enabledelayedexpansion

echo ========================================================
echo     Windows Compatibility Check Script
echo     Version: 1.0.0
echo     Time: %date% %time%
echo ========================================================
echo.

REM Check Java
echo [INFO] Checking Java environment...
where java >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java not found in PATH
    echo Please install Java 8 or higher and add it to PATH
    exit /b 1
) else (
    echo [SUCCESS] Java command available
    java -version 2>&1 | findstr "version"
)

REM Check JAVA_HOME
if not "%JAVA_HOME%"=="" (
    echo [SUCCESS] JAVA_HOME is set: %JAVA_HOME%
    if exist "%JAVA_HOME%\bin\java.exe" (
        echo [SUCCESS] JAVA_HOME points to valid Java installation
    ) else (
        echo [WARNING] JAVA_HOME points to invalid Java installation
    )
) else (
    echo [WARNING] JAVA_HOME is not set
)

REM Check Maven
echo.
echo [INFO] Checking Maven environment...
where mvn >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Maven not found in PATH
    echo Please install Apache Maven and add it to PATH
    exit /b 1
) else (
    echo [SUCCESS] Maven command available
    mvn -version 2>&1 | findstr "Apache Maven"
)

REM Check network tools
echo.
echo [INFO] Checking network tools...
where netstat >nul 2>&1
if errorlevel 1 (
    echo [ERROR] netstat not available
) else (
    echo [SUCCESS] netstat available
)

where tasklist >nul 2>&1
if errorlevel 1 (
    echo [ERROR] tasklist not available
) else (
    echo [SUCCESS] tasklist available
)

where taskkill >nul 2>&1
if errorlevel 1 (
    echo [ERROR] taskkill not available
) else (
    echo [SUCCESS] taskkill available
)

where curl >nul 2>&1
if errorlevel 1 (
    echo [WARNING] curl not available, some features may be limited
) else (
    echo [SUCCESS] curl available
)

REM Check project structure
echo.
echo [INFO] Checking project structure...
if exist "pom.xml" (
    echo [SUCCESS] Root pom.xml exists
) else (
    echo [ERROR] Root pom.xml not found
    exit /b 1
)

REM Check Windows batch scripts
if exist "scripts\set-java-env.bat" (
    echo [SUCCESS] Java environment script exists
) else (
    echo [WARNING] Java environment script not found
)

if exist "file-transfer-server-standalone\start-server.bat" (
    echo [SUCCESS] Server start script exists
) else (
    echo [WARNING] Server start script not found
)

if exist "build-and-test.bat" (
    echo [SUCCESS] Build and test script exists
) else (
    echo [WARNING] Build and test script not found
)

REM Check project modules
set "modules=file-transfer-server-sdk file-transfer-client-sdk file-transfer-client-demo file-transfer-server-standalone"
for %%m in (%modules%) do (
    if exist "%%m" (
        echo [SUCCESS] Module exists: %%m
        if exist "%%m\pom.xml" (
            echo [SUCCESS] Module pom.xml exists: %%m\pom.xml
        ) else (
            echo [WARNING] Module pom.xml not found: %%m\pom.xml
        )
    ) else (
        echo [WARNING] Module not found: %%m
    )
)

REM Test path handling
echo.
echo [INFO] Testing path handling...
set "test_dir=test-windows-compat"
if exist "%test_dir%" (
    rmdir /s /q "%test_dir%" 2>nul
)
mkdir "%test_dir%" 2>nul

if exist "%test_dir%" (
    echo [SUCCESS] Directory creation successful: %test_dir%
    
    REM Create test file
    echo Windows compatibility test > "%test_dir%\test-file.txt"
    if exist "%test_dir%\test-file.txt" (
        echo [SUCCESS] File creation successful: %test_dir%\test-file.txt
    ) else (
        echo [ERROR] File creation failed
    )
    
    REM Clean up test directory
    rmdir /s /q "%test_dir%" 2>nul
    echo [SUCCESS] Test directory cleanup completed
) else (
    echo [ERROR] Directory creation failed: %test_dir%
    exit /b 1
)

echo.
echo [SUCCESS] Windows compatibility check completed!
echo [INFO] All basic functionality checks passed
echo.
echo [INFO] Next steps:
echo   1. Run 'build-and-test.bat build' for build test
echo   2. Run 'file-transfer-server-standalone\start-server.bat start' to test server startup
echo   3. See WINDOWS_COMPATIBILITY_GUIDE.md for detailed usage instructions
echo.

exit /b 0
