@echo off
REM ================================================================================
REM Simple File Transfer Server Start Script (Windows Version)
REM ================================================================================

setlocal enabledelayedexpansion

echo ========================================================
echo     File Transfer Server Simple Start Script
echo     Version: 1.0.0
echo     Time: %date% %time%
echo ========================================================
echo.

REM Configuration
set "DEFAULT_SERVER_PORT=49011"
set "JAR_NAME=file-transfer-server-standalone-1.0.0.jar"
set "TARGET_DIR=target"
set "LOG_DIR=.\logs"
set "PID_FILE=%LOG_DIR%\server.pid"
set "LOG_FILE=%LOG_DIR%\server.log"

REM Parse command
set "COMMAND=%1"
set "BACKGROUND=false"
if "%2"=="--background" set "BACKGROUND=true"

if "%COMMAND%"=="" (
    echo Usage: %~nx0 [start^|stop^|status^|logs] [--background]
    echo.
    echo Commands:
    echo   start     Start server
    echo   stop      Stop server
    echo   status    View server status
    echo   logs      View server logs
    echo.
    echo Options:
    echo   --background  Run server in background
    echo.
    exit /b 1
)

REM Create log directory
if not exist "%LOG_DIR%" (
    mkdir "%LOG_DIR%"
    echo [INFO] Created log directory: %LOG_DIR%
)

REM Check Java
where java >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java not found in PATH
    exit /b 1
)

REM Check JAR file
set "jar_path=%TARGET_DIR%\%JAR_NAME%"
if not exist "%jar_path%" (
    echo [ERROR] JAR file does not exist: %jar_path%
    echo Please run 'mvn clean package' to compile the project first
    exit /b 1
)

REM Execute command
if "%COMMAND%"=="start" goto :start_server
if "%COMMAND%"=="stop" goto :stop_server
if "%COMMAND%"=="status" goto :show_status
if "%COMMAND%"=="logs" goto :show_logs

echo [ERROR] Unknown command: %COMMAND%
exit /b 1

:start_server
echo [INFO] Starting file transfer server...

REM Check if server is already running
if exist "%PID_FILE%" (
    set /p server_pid=<"%PID_FILE%"
    tasklist /fi "pid eq !server_pid!" 2>nul | findstr "!server_pid!" >nul
    if not errorlevel 1 (
        echo [WARNING] Server is already running (PID: !server_pid!)
        exit /b 1
    ) else (
        del "%PID_FILE%" >nul 2>&1
    )
)

REM Check port
netstat -an | findstr ":%DEFAULT_SERVER_PORT% " >nul 2>&1
if not errorlevel 1 (
    echo [ERROR] Port %DEFAULT_SERVER_PORT% is already in use
    exit /b 1
)

REM Build Java command
set "java_opts=-Xms512m -Xmx1g -XX:+UseG1GC"
set "spring_opts=--server.port=%DEFAULT_SERVER_PORT% --spring.profiles.active=server"
set "java_cmd=java %java_opts% -jar %jar_path% %spring_opts%"

echo [INFO] Java command: %java_cmd%

if "%BACKGROUND%"=="true" (
    echo [INFO] Starting server in background...
    start /b "" %java_cmd% > "%LOG_FILE%" 2>&1
    
    REM Wait a moment for startup
    timeout /t 3 /nobreak >nul
    
    REM Try to find the process and save PID
    for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv 2^>nul ^| findstr "%JAR_NAME%"') do (
        set "pid=%%i"
        echo !pid! > "%PID_FILE%"
        echo [INFO] Server starting in background (PID: !pid!)
        goto :check_startup
    )
    
    :check_startup
    REM Wait for server to be ready
    echo [INFO] Waiting for server to start...
    set /a wait_count=0
    :wait_loop
    if %wait_count% geq 30 goto :startup_timeout
    
    curl -s "http://localhost:%DEFAULT_SERVER_PORT%/filetransfer/actuator/health" >nul 2>&1
    if not errorlevel 1 (
        echo [SUCCESS] Server started successfully!
        echo [INFO] Server address: http://localhost:%DEFAULT_SERVER_PORT%
        echo [INFO] API documentation: http://localhost:%DEFAULT_SERVER_PORT%/filetransfer/doc.html
        echo [INFO] Health check: http://localhost:%DEFAULT_SERVER_PORT%/filetransfer/actuator/health
        echo [INFO] Log file: %LOG_FILE%
        exit /b 0
    )
    
    timeout /t 2 /nobreak >nul
    set /a wait_count+=2
    goto :wait_loop
    
    :startup_timeout
    echo [ERROR] Server startup timeout
    goto :stop_server
    
) else (
    echo [INFO] Starting server in foreground...
    echo [INFO] Press Ctrl+C to stop server
    %java_cmd%
)
exit /b 0

:stop_server
echo [INFO] Stopping file transfer server...

if not exist "%PID_FILE%" (
    echo [WARNING] Server is not running (PID file not found)
    exit /b 0
)

set /p server_pid=<"%PID_FILE%"
tasklist /fi "pid eq %server_pid%" 2>nul | findstr "%server_pid%" >nul
if errorlevel 1 (
    echo [WARNING] Server process not found (PID: %server_pid%)
    del "%PID_FILE%" >nul 2>&1
    exit /b 0
)

echo [INFO] Stopping server process (PID: %server_pid%)...
taskkill /pid %server_pid% >nul 2>&1

REM Wait for process to exit
set /a wait_count=0
:stop_wait_loop
if %wait_count% geq 15 goto :force_kill

tasklist /fi "pid eq %server_pid%" 2>nul | findstr "%server_pid%" >nul
if errorlevel 1 (
    del "%PID_FILE%" >nul 2>&1
    echo [SUCCESS] Server stopped
    exit /b 0
)

timeout /t 1 /nobreak >nul
set /a wait_count+=1
goto :stop_wait_loop

:force_kill
echo [WARNING] Force stopping server process...
taskkill /f /pid %server_pid% >nul 2>&1
del "%PID_FILE%" >nul 2>&1
echo [SUCCESS] Server force stopped
exit /b 0

:show_status
if exist "%PID_FILE%" (
    set /p server_pid=<"%PID_FILE%"
    tasklist /fi "pid eq %server_pid%" 2>nul | findstr "%server_pid%" >nul
    if not errorlevel 1 (
        echo [SUCCESS] Server is running (PID: %server_pid%)
        echo Server information:
        echo   PID: %server_pid%
        echo   Port: %DEFAULT_SERVER_PORT%
        echo   Server address: http://localhost:%DEFAULT_SERVER_PORT%
        echo   API documentation: http://localhost:%DEFAULT_SERVER_PORT%/filetransfer/doc.html
        echo   Health check: http://localhost:%DEFAULT_SERVER_PORT%/filetransfer/actuator/health
        echo   Log file: %LOG_FILE%
        
        curl -s "http://localhost:%DEFAULT_SERVER_PORT%/filetransfer/actuator/health" >nul 2>&1
        if not errorlevel 1 (
            echo [SUCCESS] Server health check passed
        ) else (
            echo [WARNING] Server health check failed
        )
    ) else (
        echo [INFO] Server is not running (process not found)
        del "%PID_FILE%" >nul 2>&1
    )
) else (
    echo [INFO] Server is not running (PID file not found)
)
exit /b 0

:show_logs
if exist "%LOG_FILE%" (
    echo [INFO] Showing server logs: %LOG_FILE%
    echo ========================================
    type "%LOG_FILE%"
) else (
    echo [WARNING] Log file does not exist: %LOG_FILE%
)
exit /b 0
