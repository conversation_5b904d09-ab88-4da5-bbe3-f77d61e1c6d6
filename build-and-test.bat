@echo off
REM ================================================================================
REM 文件传输SDK简化构建和测试脚本 (Windows版本)
REM ================================================================================

setlocal enabledelayedexpansion

REM ==================== 常量定义 ====================

REM 脚本版本信息
set "SCRIPT_VERSION=3.0.0"
set "SCRIPT_NAME=文件传输SDK简化构建和测试脚本"

REM 项目模块列表
set "PROJECT_MODULES=file-transfer-server-sdk file-transfer-client-sdk file-transfer-client-demo"

REM 需要单元测试的模块列表
set "TEST_MODULES=file-transfer-server-sdk file-transfer-client-sdk"

REM 演示模块列表（不执行单元测试，而是执行演示程序）
set "DEMO_MODULES=file-transfer-client-demo"

REM 独立服务模块
set "STANDALONE_MODULE=file-transfer-server-standalone"

REM 超时配置常量（秒）
set "BUILD_TIMEOUT_SECONDS=600"
set "TEST_TIMEOUT_SECONDS=1200"
set "SERVER_STARTUP_TIMEOUT_SECONDS=30"
set "SERVER_SHUTDOWN_TIMEOUT_SECONDS=15"
set "DEMO_TEST_TIMEOUT_SECONDS=300"

REM 端口配置
set "TEST_SERVER_PORT=49011"

REM 目录配置
set "LOG_DIR=.\logs"
for /f "tokens=1-3 delims=/ " %%a in ('date /t') do set "DATE_STR=%%c%%a%%b"
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set "TIME_STR=%%a%%b"
set "MAIN_LOG=%LOG_DIR%\build-and-test-%DATE_STR%_%TIME_STR%.log"
set "SERVER_PID_FILE=%LOG_DIR%\test-server.pid"

REM 执行模式常量
set "MODE_BUILD=build"
set "MODE_BUILD_TEST=build-test"

REM ==================== 日志函数 ====================

:log_info
echo [INFO] %date% %time% - %~1
echo [INFO] %date% %time% - %~1 >> "%MAIN_LOG%" 2>nul
goto :eof

:log_success
echo [SUCCESS] %date% %time% - %~1
echo [SUCCESS] %date% %time% - %~1 >> "%MAIN_LOG%" 2>nul
goto :eof

:log_warning
echo [WARNING] %date% %time% - %~1
echo [WARNING] %date% %time% - %~1 >> "%MAIN_LOG%" 2>nul
goto :eof

:log_error
echo [ERROR] %date% %time% - %~1
echo [ERROR] %date% %time% - %~1 >> "%MAIN_LOG%" 2>nul
goto :eof

:log_step
echo [STEP %~1] %date% %time% - %~2
echo [STEP %~1] %date% %time% - %~2 >> "%MAIN_LOG%" 2>nul
echo ======================================== >> "%MAIN_LOG%" 2>nul
goto :eof

:log_test_phase
echo [TEST_PHASE] %date% %time% - %~1
echo [TEST_PHASE] %date% %time% - %~1 >> "%MAIN_LOG%" 2>nul
echo ---------------------------------------- >> "%MAIN_LOG%" 2>nul
goto :eof

REM ==================== 工具函数 ====================

:init_logging
REM 创建日志目录
if not exist "%LOG_DIR%" (
    mkdir "%LOG_DIR%"
)

REM 创建主日志文件
echo. > "%MAIN_LOG%"
call :log_info "主日志文件：%MAIN_LOG%"
goto :eof

:show_header
echo ========================================================
echo     %SCRIPT_NAME%
echo     版本：%SCRIPT_VERSION%
echo     时间：%date% %time%
echo ========================================================
goto :eof

:check_command
set "command=%~1"
set "description=%~2"

where "%command%" >nul 2>&1
if errorlevel 1 (
    call :log_error "%description% 未安装或未在PATH中：%command%"
    exit /b 1
)
exit /b 0

:check_port_available
set "port=%~1"
netstat -an | findstr ":%port% " >nul 2>&1
if not errorlevel 1 (
    exit /b 1
)
exit /b 0

REM ==================== 环境检查函数 ====================

:setup_java_environment
set "custom_java_home=%~1"

call :log_step "1" "设置Java环境"

REM 如果指定了自定义Java路径，则临时修改环境变量
if not "%custom_java_home%"=="" (
    if exist "%custom_java_home%\bin\java.exe" (
        set "JAVA_HOME=%custom_java_home%"
        set "PATH=%custom_java_home%\bin;%PATH%"
        set "MAVEN_OPTS=-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
        set "MAVEN_OPTS=!MAVEN_OPTS! -Djava.security.policy=all.policy"
        set "MAVEN_OPTS=!MAVEN_OPTS! -Djava.home=%custom_java_home%"
        call :log_info "使用指定的Java JDK：%custom_java_home%"
        goto :java_env_done
    ) else (
        call :log_error "指定的Java JDK路径无效：%custom_java_home%"
        exit /b 1
    )
) else (
    REM 使用set-java-env.bat脚本设置环境变量
    set "set_java_script=.\scripts\set-java-env.bat"
    if exist "!set_java_script!" (
        call :log_info "调用Java环境设置脚本：!set_java_script!"
        call "!set_java_script!" >> "%MAIN_LOG%" 2>&1
        if not errorlevel 1 (
            call :log_info "Java环境设置脚本执行成功"
        ) else (
            call :log_warning "Java环境设置脚本执行失败，尝试使用系统默认Java"
        )
    ) else (
        call :log_warning "未找到Java环境设置脚本：!set_java_script!"
        call :log_warning "使用系统默认Java环境"
    )
)

:java_env_done
REM 验证Java命令可用性
call :check_command "java" "Java运行时"
if errorlevel 1 exit /b 1

REM 获取Java版本信息
for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do (
    set "java_version=%%i"
    goto :java_version_done
)
:java_version_done
call :log_info "当前Java版本：%java_version%"

REM 验证Java版本兼容性
echo %java_version% | findstr "1.8." >nul
if not errorlevel 1 (
    call :log_success "使用Java 8，完全兼容"
) else (
    echo %java_version% | findstr /r "^\"1[1-9]\." >nul
    if not errorlevel 1 (
        call :log_warning "使用Java %java_version%，项目配置为Java 8，但应该向后兼容"
    ) else (
        call :log_warning "当前Java版本：%java_version%，可能存在兼容性问题"
    )
)

exit /b 0

:check_maven_environment
call :log_step "2" "检查Maven环境"

REM 检查Maven命令
call :check_command "mvn" "Apache Maven"
if errorlevel 1 exit /b 1

REM 获取Maven版本信息
for /f "tokens=*" %%i in ('mvn -version 2^>^&1 ^| findstr "Apache Maven"') do (
    set "maven_version=%%i"
    goto :maven_version_done
)
:maven_version_done
call :log_info "Maven版本：%maven_version%"

REM 如果没有通过set-java-env.bat设置MAVEN_OPTS，则进行默认设置
if "%MAVEN_OPTS%"=="" (
    call :log_info "MAVEN_OPTS未设置，使用默认配置"
    set "MAVEN_OPTS=-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
    
    if not "%JAVA_HOME%"=="" (
        set "MAVEN_OPTS=!MAVEN_OPTS! -Djava.home=%JAVA_HOME%"
        call :log_info "Maven配置使用Java：%JAVA_HOME%"
    )
    
    REM 检查javadoc命令是否可用
    if not "%JAVA_HOME%"=="" (
        if not exist "%JAVA_HOME%\bin\javadoc.exe" (
            call :log_warning "javadoc命令不可用，将跳过Javadoc生成"
            set "MAVEN_OPTS=!MAVEN_OPTS! -Dmaven.javadoc.skip=true"
        )
    ) else (
        call :log_warning "JAVA_HOME未设置，将跳过Javadoc生成"
        set "MAVEN_OPTS=!MAVEN_OPTS! -Dmaven.javadoc.skip=true"
    )
) else (
    call :log_info "使用已设置的MAVEN_OPTS"
)

call :log_info "Maven选项：%MAVEN_OPTS%"
call :log_success "Maven环境检查完成"

exit /b 0

:validate_project_structure
call :log_step "3" "验证项目结构"

REM 检查根目录pom.xml
if not exist "pom.xml" (
    call :log_error "根目录pom.xml文件不存在"
    exit /b 1
)
call :log_info "根目录pom.xml文件存在"

REM 检查各个模块目录
set "missing_modules="
for %%m in (%PROJECT_MODULES%) do (
    if not exist "%%m" (
        set "missing_modules=!missing_modules! %%m"
    ) else (
        call :log_info "模块目录存在：%%m"
        
        REM 检查模块的pom.xml
        if not exist "%%m\pom.xml" (
            call :log_warning "模块pom.xml不存在：%%m\pom.xml"
        )
    )
)

REM 检查独立服务模块（仅在build-test模式下需要）
if not exist "%STANDALONE_MODULE%" (
    call :log_warning "独立服务模块不存在：%STANDALONE_MODULE%"
    call :log_warning "在build-test模式下将无法启动测试服务器"
) else (
    call :log_info "独立服务模块存在：%STANDALONE_MODULE%"
)

REM 报告缺失的模块
if not "%missing_modules%"=="" (
    call :log_warning "以下模块目录不存在：%missing_modules%"
    call :log_warning "将跳过这些模块的编译"
)

call :log_success "项目结构验证完成"
exit /b 0

:clean_environment
call :log_step "4" "清理构建和测试环境"

REM 清理Maven构建缓存
call :log_info "清理Maven构建缓存..."

REM 清理根目录target
if exist "target" (
    rmdir /s /q "target" 2>nul
    call :log_info "清理根目录target目录"
)

REM 清理各模块的target目录
for %%m in (%PROJECT_MODULES%) do (
    if exist "%%m" (
        if exist "%%m\target" (
            rmdir /s /q "%%m\target" 2>nul
            call :log_info "清理模块target目录：%%m"
        )
    )
)

REM 清理独立服务模块的target目录
if exist "%STANDALONE_MODULE%" (
    if exist "%STANDALONE_MODULE%\target" (
        rmdir /s /q "%STANDALONE_MODULE%\target" 2>nul
        call :log_info "清理独立服务模块target目录：%STANDALONE_MODULE%"
    )
)

REM 清理测试数据目录
set "test_data_dirs=.\test-data .\data ./%STANDALONE_MODULE%/data"
for %%d in (%test_data_dirs%) do (
    if exist "%%d" (
        rmdir /s /q "%%d" 2>nul
        call :log_info "清理测试数据目录：%%d"
    )
)

REM 清理临时文件
del /s /q "*.tmp" 2>nul
del /s /q "test-*.dat" 2>nul
del /s /q "*.test" 2>nul

REM 停止可能运行的测试服务器进程
call :stop_all_test_servers

call :log_success "环境清理完成"
exit /b 0

:stop_all_test_servers
call :log_info "停止所有可能运行的测试服务器进程..."

REM 停止占用测试端口的进程
for /f "tokens=5" %%i in ('netstat -ano ^| findstr ":%TEST_SERVER_PORT% "') do (
    set "test_server_pid=%%i"
    if not "!test_server_pid!"=="" (
        call :log_info "停止占用端口 %TEST_SERVER_PORT% 的进程：!test_server_pid!"
        taskkill /f /pid !test_server_pid! >nul 2>&1
    )
)

REM 清理PID文件
if exist "%SERVER_PID_FILE%" (
    del "%SERVER_PID_FILE%" >nul 2>&1
    call :log_info "清理服务器PID文件"
)
goto :eof

REM ==================== 构建功能函数 ====================

:compile_project
call :log_step "5" "编译项目"

call :get_current_time_seconds
set "start_time=!current_time_seconds!"

call :log_info "开始编译整个项目..."
call :log_info "编译命令：mvn clean compile -T 1C"

REM 执行Maven编译，使用并行编译提高速度
mvn clean compile -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Dmaven.compiler.encoding=UTF-8 -Dproject.build.sourceEncoding=UTF-8 >> "%MAIN_LOG%" 2>&1
set "compile_result=!errorlevel!"

call :get_current_time_seconds
set "end_time=!current_time_seconds!"
set /a duration=end_time-start_time

if !compile_result! equ 0 (
    call :log_success "项目编译成功，耗时：%duration%秒"
    exit /b 0
) else (
    call :log_error "项目编译失败，耗时：%duration%秒"
    call :log_error "详细错误信息请查看日志文件：%MAIN_LOG%"
    exit /b 1
)

:install_project
call :log_step "6" "安装项目到本地Maven仓库"

call :get_current_time_seconds
set "start_time=!current_time_seconds!"

call :log_info "开始安装项目到本地Maven仓库..."
call :log_info "安装命令：mvn install -DskipTests -T 1C"

REM 执行Maven安装，跳过测试以提高速度
mvn install -DskipTests -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Dmaven.compiler.encoding=UTF-8 -Dproject.build.sourceEncoding=UTF-8 >> "%MAIN_LOG%" 2>&1
set "install_result=!errorlevel!"

call :get_current_time_seconds
set "end_time=!current_time_seconds!"
set /a duration=end_time-start_time

if !install_result! equ 0 (
    call :log_success "项目安装成功，耗时：%duration%秒"
    exit /b 0
) else (
    call :log_error "项目安装失败，耗时：%duration%秒"
    call :log_error "详细错误信息请查看日志文件：%MAIN_LOG%"
    exit /b 1
)

:verify_build_results
call :log_step "7" "验证构建结果"

set /a success_count=0
set /a total_count=0

REM 检查各模块的编译结果
for %%m in (%PROJECT_MODULES%) do (
    if exist "%%m" (
        set /a total_count+=1

        REM 检查target/classes目录是否存在
        if exist "%%m\target\classes" (
            REM 计算class文件数量
            set "class_count=0"
            for /r "%%m\target\classes" %%f in (*.class) do set /a class_count+=1

            if !class_count! gtr 0 (
                call :log_info "模块编译成功：%%m (生成 !class_count! 个class文件)"
                set /a success_count+=1
            ) else (
                call :log_warning "模块编译异常：%%m (未生成class文件)"
            )
        ) else (
            call :log_warning "模块编译失败：%%m (target/classes目录不存在)"
        )

        REM 检查JAR文件是否生成
        if exist "%%m\target" (
            set "jar_count=0"
            for %%j in ("%%m\target\*.jar") do set /a jar_count+=1
            if !jar_count! gtr 0 (
                call :log_info "模块JAR文件生成：%%m (!jar_count! 个JAR文件)"
            )
        )
    )
)

REM 输出验证结果
call :log_info "编译验证结果：%success_count%/%total_count% 个模块编译成功"

if %success_count% equ %total_count% (
    call :log_success "所有模块编译验证通过"
    exit /b 0
) else (
    call :log_warning "部分模块编译验证失败"
    exit /b 1
)

REM ==================== 辅助函数 ====================

:get_current_time_seconds
REM 获取当前时间的秒数（简化版本）
for /f "tokens=1-3 delims=:." %%a in ('echo %time%') do (
    set /a current_time_seconds=%%a*3600+%%b*60+%%c
)
goto :eof

REM ==================== 测试功能函数（简化版本） ====================

:run_unit_tests
call :log_step "8" "运行单元测试"
call :log_test_phase "开始单元测试阶段"

set /a total_modules=0
set /a success_modules=0

call :get_current_time_seconds
set "start_time=!current_time_seconds!"

REM 只对需要测试的模块执行单元测试
for %%m in (%TEST_MODULES%) do (
    if exist "%%m" (
        set /a total_modules+=1

        call :log_test_phase "运行模块单元测试：%%m"

        REM 运行单元测试，排除集成测试
        mvn test -pl "%%m" -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Dtest="!**/*IntegrationTest,!**/*EndToEndTest" >> "%MAIN_LOG%" 2>&1

        if not errorlevel 1 (
            call :log_success "模块单元测试通过：%%m"
            set /a success_modules+=1
        ) else (
            call :log_error "模块单元测试失败：%%m"
        )
    ) else (
        call :log_warning "模块目录不存在，跳过：%%m"
    )
)

REM 跳过演示模块的单元测试，记录说明信息
for %%d in (%DEMO_MODULES%) do (
    if exist "%%d" (
        call :log_info "跳过演示模块单元测试：%%d（演示模块将在集成测试阶段执行）"
    )
)

call :get_current_time_seconds
set "end_time=!current_time_seconds!"
set /a duration=end_time-start_time

call :log_test_phase "单元测试阶段完成"
call :log_info "单元测试结果：%success_modules%/%total_modules% 个模块通过，耗时：%duration%秒"

if %success_modules% equ %total_modules% (
    call :log_success "所有单元测试通过"
    exit /b 0
) else (
    call :log_error "部分单元测试失败"
    exit /b 1
)

:show_help
echo ========================================================
echo     %SCRIPT_NAME%
echo     版本：%SCRIPT_VERSION%
echo ========================================================
echo.
echo 用法: %~nx0 [模式] [选项]
echo.
echo 执行模式：
echo   build                 仅执行构建（编译+安装），不运行测试
echo   build-test            执行完整流程（构建+测试）[默认]
echo.
echo Java环境选项：
echo   --java-home PATH      指定Java JDK路径
echo.
echo 其他选项：
echo   --help                显示此帮助信息
echo.
echo 使用示例：
echo   %~nx0                              # 完整构建和测试流程
echo   %~nx0 build                        # 仅构建项目
echo   %~nx0 build-test                   # 构建并测试项目
echo   %~nx0 --java-home C:\Java\jdk8     # 使用指定的Java路径
echo.
echo 默认配置：
echo   Java路径: 由scripts\set-java-env.bat脚本管理
echo   测试端口: %TEST_SERVER_PORT%
echo   日志目录: %LOG_DIR%
echo.
echo 说明：
echo   - build模式：编译项目并安装到本地Maven仓库
echo   - build-test模式：在build基础上运行单元测试和集成测试
echo   - 集成测试会自动启动和停止file-transfer-standalone服务
echo   - 所有日志都会记录到日志文件中，便于问题排查
echo.
goto :eof

REM ==================== 主程序 ====================

:main
REM 解析命令行参数
set "custom_java_home="
set "execution_mode=%MODE_BUILD_TEST%"

:parse_main_args
if "%~1"=="" goto :main_args_done
if "%~1"=="build" (
    set "execution_mode=%MODE_BUILD%"
    shift
    goto :parse_main_args
)
if "%~1"=="build-test" (
    set "execution_mode=%MODE_BUILD_TEST%"
    shift
    goto :parse_main_args
)
if "%~1"=="--java-home" (
    set "custom_java_home=%~2"
    shift
    shift
    goto :parse_main_args
)
if "%~1"=="--help" (
    call :show_help
    exit /b 0
)
call :log_error "未知选项: %~1"
echo.
call :show_help
exit /b 1

:main_args_done
REM 显示脚本头部信息
call :show_header

REM 初始化日志
call :init_logging

call :log_info "执行模式：%execution_mode%"

REM 执行主要流程
set "execution_failed=false"

REM 步骤1-4：环境检查和准备
call :setup_java_environment "%custom_java_home%"
if errorlevel 1 set "execution_failed=true"

if "%execution_failed%"=="false" (
    call :check_maven_environment
    if errorlevel 1 set "execution_failed=true"
)

if "%execution_failed%"=="false" (
    call :validate_project_structure
    if errorlevel 1 set "execution_failed=true"
)

if "%execution_failed%"=="false" (
    call :clean_environment
    if errorlevel 1 set "execution_failed=true"
)

REM 步骤5-7：构建流程
if "%execution_failed%"=="false" (
    call :compile_project
    if errorlevel 1 set "execution_failed=true"
)

if "%execution_failed%"=="false" (
    call :install_project
    if errorlevel 1 set "execution_failed=true"
)

if "%execution_failed%"=="false" (
    call :verify_build_results
    if errorlevel 1 set "execution_failed=true"
)

REM 步骤8：测试流程（仅在build-test模式下执行）
if "%execution_failed%"=="false" (
    if "%execution_mode%"=="%MODE_BUILD_TEST%" (
        call :log_test_phase "开始测试流程"

        call :run_unit_tests
        if errorlevel 1 set "execution_failed=true"

        REM 注意：集成测试功能在Windows版本中简化，仅运行单元测试
        call :log_info "Windows版本暂时跳过集成测试，仅运行单元测试"
    )
)

REM 返回结果
if "%execution_failed%"=="true" (
    call :log_error "执行失败"
    exit /b 1
) else (
    if "%execution_mode%"=="%MODE_BUILD%" (
        call :log_success "构建成功完成"
    ) else (
        call :log_success "构建和测试成功完成"
    )
    exit /b 0
)

REM 执行主函数
call :main %*
