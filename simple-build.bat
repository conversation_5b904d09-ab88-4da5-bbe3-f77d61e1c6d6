@echo off
REM ================================================================================
REM Simple Build Script for File Transfer SDK (Windows Version)
REM ================================================================================

echo ========================================================
echo     File Transfer SDK Simple Build Script
echo     Version: 1.0.0
echo     Time: %date% %time%
echo ========================================================
echo.

REM Check if this is build or build-test mode
set "MODE=build"
if "%1"=="build-test" set "MODE=build-test"

echo [INFO] Execution mode: %MODE%
echo.

REM Step 1: Setup Java environment
echo [STEP 1] Setting up Java environment...
if exist "scripts\set-java-env-en.bat" (
    echo [INFO] Calling Java environment setup script...
    call "scripts\set-java-env-en.bat"
    if errorlevel 1 (
        echo [ERROR] Java environment setup failed
        exit /b 1
    )
) else (
    echo [WARNING] Java environment script not found, using system default
)

REM Verify Java
where java >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java not found in PATH
    exit /b 1
)
echo [SUCCESS] Java environment ready

REM Step 2: Check Maven
echo.
echo [STEP 2] Checking Maven environment...
where mvn >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Maven not found in PATH
    exit /b 1
)
echo [SUCCESS] Maven environment ready

REM Step 3: Validate project structure
echo.
echo [STEP 3] Validating project structure...
if not exist "pom.xml" (
    echo [ERROR] Root pom.xml not found
    exit /b 1
)
echo [SUCCESS] Project structure validated

REM Step 4: Clean environment
echo.
echo [STEP 4] Cleaning build environment...
if exist "target" rmdir /s /q "target" 2>nul
for %%m in (file-transfer-server-sdk file-transfer-client-sdk file-transfer-client-demo file-transfer-server-standalone) do (
    if exist "%%m\target" (
        rmdir /s /q "%%m\target" 2>nul
        echo [INFO] Cleaned: %%m\target
    )
)
echo [SUCCESS] Environment cleaned

REM Step 5: Compile project
echo.
echo [STEP 5] Compiling project...
echo [INFO] Running: mvn clean compile -T 1C
mvn clean compile -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
if errorlevel 1 (
    echo [ERROR] Project compilation failed
    exit /b 1
)
echo [SUCCESS] Project compilation completed

REM Step 6: Install project
echo.
echo [STEP 6] Installing project to local Maven repository...
echo [INFO] Running: mvn install -DskipTests -T 1C
mvn install -DskipTests -T 1C -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
if errorlevel 1 (
    echo [ERROR] Project installation failed
    exit /b 1
)
echo [SUCCESS] Project installation completed

REM Step 7: Run tests (if in build-test mode)
if "%MODE%"=="build-test" (
    echo.
    echo [STEP 7] Running unit tests...
    echo [INFO] Running: mvn test -Dtest=!**/*IntegrationTest,!**/*EndToEndTest
    mvn test -Dtest=!**/*IntegrationTest,!**/*EndToEndTest
    if errorlevel 1 (
        echo [ERROR] Unit tests failed
        exit /b 1
    )
    echo [SUCCESS] Unit tests completed
)

echo.
echo ========================================================
if "%MODE%"=="build" (
    echo [SUCCESS] Build completed successfully!
) else (
    echo [SUCCESS] Build and test completed successfully!
)
echo ========================================================
echo.

exit /b 0
