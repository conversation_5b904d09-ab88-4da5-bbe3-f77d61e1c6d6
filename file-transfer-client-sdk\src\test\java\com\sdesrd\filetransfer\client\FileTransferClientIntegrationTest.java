package com.sdesrd.filetransfer.client;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.exception.FileTransferException;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.FileUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输客户端单元测试
 * 使用Mock服务端进行测试，不依赖实际的服务端实例
 */
@Slf4j
@DisplayName("文件传输客户端单元测试")
class FileTransferClientIntegrationTest {
    
    @TempDir
    Path tempDir;
    
    private FileTransferClient client;
    private File testFile;
    private TestTransferListener listener;
    
    @BeforeEach
    void setUp() throws IOException {
        // 创建客户端配置
        ClientConfig config = ClientConfigBuilder.localConfig("demo", "demo-secret-key-2024");
        config.setChunkSize(1024 * 1024); // 1MB 分块，便于测试
        config.setMaxConcurrentTransfers(2);
        config.setRetryCount(3);
        
        client = new FileTransferClient(config);
        
        // 创建测试文件
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            content.append("这是一个测试文件内容，用于验证文件传输功能。\n");
        }
        testFile = createTestFile("test-file.txt", content.toString());
        
        // 创建测试监听器
        listener = new TestTransferListener();
    }
    
    @AfterEach
    void tearDown() {
        if (client != null) {
            client.close();
        }

        // 清理测试文件 - 在删除前恢复可写权限
        if (testFile != null && testFile.exists()) {
            try {
                // 确保文件可写，以防在测试中被设置为只读
                testFile.setWritable(true);
                if (!testFile.delete()) {
                    log.warn("删除测试文件失败: {}", testFile.getAbsolutePath());
                }
            } catch (Exception e) {
                log.error("清理测试文件时发生异常: {}", testFile.getAbsolutePath(), e);
            }
        }
    }
    
    @Test
    @DisplayName("客户端配置测试")
    void testClientConfiguration() {
        // 验证客户端配置
        assertNotNull(client);

        // 测试文件存在性验证
        assertTrue(testFile.exists());
        assertTrue(testFile.length() > 0);

        // 验证监听器初始状态
        assertFalse(listener.isStartCalled());
        assertFalse(listener.isProgressCalled());
        assertFalse(listener.isCompletedCalled());
        assertFalse(listener.isErrorCalled());
    }
    
    @Test
    @DisplayName("文件不存在异常测试")
    void testFileNotExistsException() {
        // 尝试上传不存在的文件（使用客户端指定fileId的新API）
        assertThrows(FileTransferException.class, () -> {
            String testFileId = generateTestUlid();
            client.uploadFileSyncWithId("/path/to/nonexistent/file.txt", testFileId, null, null);
        });
    }
    
    @Test
    @DisplayName("配置验证测试")
    void testConfigValidation() {
        // 测试用户名和密钥配置
        ClientConfig validConfig = new ClientConfig();
        validConfig.setUser("test-user");
        validConfig.setSecretKey("test-secret");
        
        assertNotNull(validConfig.getUser());
        assertNotNull(validConfig.getSecretKey());
        assertFalse(validConfig.getUser().isEmpty());
        assertFalse(validConfig.getSecretKey().isEmpty());
    }
    
    @Test
    @DisplayName("文件MD5计算测试")
    void testFileMD5Calculation() throws Exception {
        // 计算测试文件的MD5
        String md5 = FileUtils.calculateMD5(testFile);

        // 验证MD5格式
        assertNotNull(md5);
        assertEquals(32, md5.length()); // MD5应该是32位十六进制字符串
        assertTrue(md5.matches("[a-fA-F0-9]{32}")); // 验证是否为有效的十六进制
    }
    
    @Test
    @DisplayName("客户端资源管理测试")
    void testClientResourceManagement() {
        // 测试客户端可以正常关闭
        assertDoesNotThrow(() -> {
            client.close();
        });

        // 重新创建客户端用于后续测试
        ClientConfig config = ClientConfigBuilder.localConfig("demo", "demo-secret-key-2024");
        config.setChunkSize(1024 * 1024);
        config.setMaxConcurrentTransfers(2);
        config.setRetryCount(3);

        client = new FileTransferClient(config);
        assertNotNull(client);
    }
    
    @Test
    @DisplayName("大文件创建和MD5计算测试")
    void testLargeFileHandling() throws Exception {
        // 创建较大的测试文件（1MB）
        StringBuilder largeContent = new StringBuilder();
        String chunk = "";
        for (int i = 0; i < 1024; i++) {
            chunk += "X";
        }
        for (int i = 0; i < 1024; i++) {
            largeContent.append(chunk);
        }
        File largeFile = createTestFile("large-test-file.dat", largeContent.toString()); // 1MB

        try {
            // 验证文件创建成功
            assertTrue(largeFile.exists());
            assertEquals(1024 * 1024, largeFile.length()); // 1MB

            // 计算MD5
            String md5 = FileUtils.calculateMD5(largeFile);
            assertNotNull(md5);
            assertEquals(32, md5.length());

            // 验证相同内容的文件有相同的MD5
            File largeFile2 = createTestFile("large-test-file2.dat", largeContent.toString());
            String md5_2 = FileUtils.calculateMD5(largeFile2);
            assertEquals(md5, md5_2);

            // 清理第二个文件
            if (largeFile2.exists()) {
                largeFile2.delete();
            }
        } finally {
            // 清理大文件
            if (largeFile.exists()) {
                largeFile.delete();
            }
        }
    }
    
    /**
     * 创建测试文件
     */
    private File createTestFile(String fileName, String content) throws IOException {
        File file = tempDir.resolve(fileName).toFile();
        Files.write(file.toPath(), content.getBytes("UTF-8"));
        return file;
    }
    
    /**
     * 测试传输监听器
     */
    private static class TestTransferListener implements TransferListener {
        private boolean startCalled = false;
        private boolean progressCalled = false;
        private boolean completedCalled = false;
        private boolean errorCalled = false;
        private int progressCallCount = 0;
        private TransferProgress finalProgress;
        
        public void onStart(TransferProgress progress) {
            startCalled = true;
        }

        public void onProgress(TransferProgress progress) {
            progressCalled = true;
            progressCallCount++;
            finalProgress = progress;
        }

        public void onCompleted(TransferProgress progress) {
            completedCalled = true;
            finalProgress = progress;
        }

        public void onError(TransferProgress progress, Throwable error) {
            errorCalled = true;
        }
        
        // Getters
        public boolean isStartCalled() { return startCalled; }
        public boolean isProgressCalled() { return progressCalled; }
        public boolean isCompletedCalled() { return completedCalled; }
        public boolean isErrorCalled() { return errorCalled; }
        public int getProgressCallCount() { return progressCallCount; }
        public TransferProgress getFinalProgress() { return finalProgress; }
    }
    

    

    
    @Test
    @DisplayName("客户端配置构建器测试")
    void testClientConfigBuilder() {
        // 测试配置构建器
        ClientConfig config = ClientConfigBuilder.localConfig("test-user", "test-secret-key");
        
        assertNotNull(config);
        assertEquals("test-user", config.getUser());
        assertEquals("test-secret-key", config.getSecretKey());
        assertEquals("localhost", config.getServerAddr());
        assertEquals(49011, config.getServerPort());
    }
    
    @Test
    @DisplayName("文件信息DTO测试")
    void testFileInfoDto() {
        // 创建模拟的文件信息对象
        com.sdesrd.filetransfer.client.dto.FileInfo fileInfo = new com.sdesrd.filetransfer.client.dto.FileInfo();
        fileInfo.setFileId("test123456789012");
        fileInfo.setFileName("test-document.pdf");
        fileInfo.setFileSize(1024 * 1024L); // 1MB

        fileInfo.setUploadTime("2024-12-01 10:30:00");
        
        // 验证文件信息
        assertEquals("test123456789012", fileInfo.getFileId());
        assertEquals("test-document.pdf", fileInfo.getFileName());
        assertEquals(1024 * 1024L, fileInfo.getFileSize());

        
        // 验证格式化大小
        String formattedSize = formatFileSize(fileInfo.getFileSize());
        assertEquals("1.0MB", formattedSize);
    }
    
    @Test
    @DisplayName("下载结果DTO测试")
    void testDownloadResultDto() {
        // 创建下载结果对象
        DownloadResult result = new DownloadResult();
        result.setSuccess(true);
        result.setFileId("download123456789");
        result.setLocalPath("/tmp/downloaded-file.txt");
        result.setFileSize(2048L);
        
        // 验证下载结果
        assertTrue(result.isSuccess());
        assertEquals("download123456789", result.getFileId());
        assertEquals("/tmp/downloaded-file.txt", result.getLocalPath());
        assertEquals(2048L, result.getFileSize());
        
        // 测试错误情况
        DownloadResult errorResult = new DownloadResult();
        errorResult.setSuccess(false);
        errorResult.setErrorMessage("下载失败：网络连接中断");
        
        assertFalse(errorResult.isSuccess());
        assertEquals("下载失败：网络连接中断", errorResult.getErrorMessage());
    }
    
    @Test
    @DisplayName("上传结果DTO测试")  
    void testUploadResultDto() {
        // 创建上传结果对象
        UploadResult result = new UploadResult();
        result.setSuccess(true);
        result.setFileId("upload123456789");
        result.setTransferId("transfer123456789");
        result.setFileName("uploaded-file.txt");
        result.setFileSize(4096L);
        
        // 验证上传结果
        assertTrue(result.isSuccess());
        assertEquals("upload123456789", result.getFileId());
        assertEquals("transfer123456789", result.getTransferId());
        assertEquals("uploaded-file.txt", result.getFileName());
        assertEquals(4096L, result.getFileSize());
    }
    
    @Test
    @DisplayName("传输进度计算测试")
    void testTransferProgressCalculation() {
        // 创建传输进度对象
        TransferProgress progress = new TransferProgress();
        progress.setTransferId("progress123456789");
        progress.setFileName("progress-test.txt");
        progress.setTotalSize(10240L); // 10KB
        progress.setTransferredSize(5120L); // 5KB
        progress.setProgress(50.0); // 手动设置进度百分比
        progress.setCompleted(false);

        // 验证进度计算
        assertEquals(50.0, progress.getProgress(), 0.01);
        assertEquals(5120L, progress.getTransferredSize());
        assertEquals(10240L, progress.getTotalSize());
        assertFalse(progress.isCompleted());

        // 测试完成状态
        progress.setTransferredSize(10240L);
        progress.setProgress(100.0); // 手动设置完成进度
        progress.setCompleted(true);
        assertEquals(100.0, progress.getProgress(), 0.01);
        assertTrue(progress.isCompleted());
    }
    
    @Test
    @DisplayName("客户端统一配置测试")
    void testClientUnifiedConfig() {
        // 创建统一配置
        ClientConfig config = ClientConfigBuilder.create()
            .serverAddr("test.example.com")
            .serverPort(8080)
            .auth("auth-test-user", "auth-test-secret-key-2024")
            .build();
        
        // 验证配置
        assertEquals("auth-test-user", config.getUser());
        assertEquals("auth-test-secret-key-2024", config.getSecretKey());
        assertEquals("test.example.com", config.getServerAddr());
        assertEquals(8080, config.getServerPort());
        
        // 测试配置的完整性
        assertNotNull(config.getUser());
        assertNotNull(config.getSecretKey());
        assertFalse(config.getUser().isEmpty());
        assertFalse(config.getSecretKey().isEmpty());
    }
    

    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + "B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1fKB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1fMB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1fGB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 生成测试用ULID
     * 简化版本，用于测试环境
     */
    private String generateTestUlid() {
        // 使用当前时间戳和随机数生成类似ULID的字符串
        long timestamp = System.currentTimeMillis();
        String timestampPart = Long.toString(timestamp, 32).toUpperCase();

        // 补齐到10位
        while (timestampPart.length() < 10) {
            timestampPart = "0" + timestampPart;
        }

        // 生成16位随机部分
        StringBuilder randomPart = new StringBuilder();
        String chars = "0123456789ABCDEFGHJKMNPQRSTVWXYZ";
        for (int i = 0; i < 16; i++) {
            randomPart.append(chars.charAt((int) (Math.random() * chars.length())));
        }

        return timestampPart + randomPart.toString();
    }
}
